#!/usr/bin/env python3
"""
测试字体修复效果的脚本
"""

from docx import Document
from docx.oxml.shared import qn

def analyze_document_fonts(filename):
    """
    分析文档中的字体使用情况
    """
    print(f"分析文档: {filename}")
    print("=" * 50)
    
    try:
        doc = Document(filename)
        font_info = {}
        
        for paragraph in doc.paragraphs:
            for run in paragraph.runs:
                if run.text.strip():  # 只分析有文本内容的run
                    font_name = run.font.name or "未设置"
                    
                    # 获取字体元素的详细信息
                    font_element = run.font.element
                    ascii_font = font_element.get(qn('w:ascii')) or "未设置"
                    east_asia_font = font_element.get(qn('w:eastAsia')) or "未设置"
                    hansi_font = font_element.get(qn('w:hAnsi')) or "未设置"
                    cs_font = font_element.get(qn('w:cs')) or "未设置"
                    
                    font_key = f"主字体:{font_name}, ASCII:{ascii_font}, 东亚:{east_asia_font}, HAnsi:{hansi_font}, CS:{cs_font}"
                    
                    if font_key not in font_info:
                        font_info[font_key] = {
                            'count': 0,
                            'sample_text': run.text[:20] + ('...' if len(run.text) > 20 else '')
                        }
                    font_info[font_key]['count'] += 1
        
        print(f"发现 {len(font_info)} 种不同的字体配置:")
        print()
        
        for font_config, info in sorted(font_info.items(), key=lambda x: x[1]['count'], reverse=True):
            print(f"字体配置: {font_config}")
            print(f"  使用次数: {info['count']}")
            print(f"  示例文本: '{info['sample_text']}'")
            print()
            
    except Exception as e:
        print(f"分析文档时出错: {e}")

def compare_documents():
    """
    比较原始文档和合并后文档的字体情况
    """
    print("字体修复效果测试")
    print("=" * 60)
    
    # 分析一个原始文档作为参考
    sample_files = ["./bk/安同良.docx", "./bk/艾四林.docx"]
    
    for sample_file in sample_files:
        try:
            analyze_document_fonts(sample_file)
            break
        except:
            continue
    
    print("\n" + "=" * 60)
    
    # 分析合并后的文档
    analyze_document_fonts("merged_document.docx")

if __name__ == "__main__":
    compare_documents()
