import os
import glob
from docx import Document
from docx.enum.text import WD_BREAK
import copy
from docx.oxml.shared import qn
from docx.oxml import OxmlElement
from docx.shared import RGBColor

def copy_font_properties(source_font, target_font):
    """
    完整复制字体属性，包括中文字体、ASCII字体等
    """
    try:
        # 复制基本字体名称
        if source_font.name:
            target_font.name = source_font.name

        # 复制字体大小
        if source_font.size:
            target_font.size = source_font.size

        # 复制字体颜色
        if source_font.color.rgb:
            target_font.color.rgb = source_font.color.rgb

        # 获取源字体的XML元素
        source_element = source_font.element
        target_element = target_font.element

        # 复制所有字体相关的XML属性
        font_attributes = [
            qn('w:ascii'),      # ASCII字符字体
            qn('w:eastAsia'),   # 东亚字符字体（中文、日文、韩文）
            qn('w:hAnsi'),      # 高位ANSI字符字体
            qn('w:cs'),         # 复杂脚本字体
            qn('w:hint'),       # 字体提示
        ]

        for attr in font_attributes:
            if source_element.get(attr):
                target_element.set(attr, source_element.get(attr))

        # 复制字体主题信息
        theme_attributes = [
            qn('w:asciiTheme'),
            qn('w:eastAsiaTheme'),
            qn('w:hAnsiTheme'),
            qn('w:cstheme'),
        ]

        for attr in theme_attributes:
            if source_element.get(attr):
                target_element.set(attr, source_element.get(attr))

    except Exception as e:
        print(f"复制字体属性时出错: {e}")

def merge_word_documents(directory, output_filename="merged_document.docx"):
    """
    将指定目录下的所有 Word 文档合并为一个文档，每个文档内容用分页符分割。
    """
    merged_document = Document()

    word_files = glob.glob(os.path.join(directory, "*.docx")) + glob.glob(os.path.join(directory, "*.doc"))

    if not word_files:
        print(f"在目录 '{directory}' 中没有找到任何 Word 文档。")
        return

    for i, file_path in enumerate(word_files):
        try:
            try:
                doc = Document(file_path)
            except Exception as e:
                print(f"打开文档失败：{file_path}，尝试不指定编码打开：{e}")
                doc = Document(file_path)

            # 检查文档是否为空（更全面的检查）
            if not doc.element.body.getchildren():
                print(f"文档 '{file_path}' 为空，已跳过。")
                continue
            
            # 复制文档内容，包括样式
            for paragraph in doc.paragraphs:
                new_paragraph = merged_document.add_paragraph()
                new_paragraph.text = paragraph.text
                
                # 复制段落样式
                if paragraph.style:
                    new_paragraph.style = paragraph.style
                
                # 复制段落格式
                new_paragraph.paragraph_format.alignment = paragraph.paragraph_format.alignment
                new_paragraph.paragraph_format.left_indent = paragraph.paragraph_format.left_indent
                new_paragraph.paragraph_format.right_indent = paragraph.paragraph_format.right_indent
                new_paragraph.paragraph_format.first_line_indent = paragraph.paragraph_format.first_line_indent
                new_paragraph.paragraph_format.space_before = paragraph.paragraph_format.space_before
                new_paragraph.paragraph_format.space_after = paragraph.paragraph_format.space_after
                new_paragraph.paragraph_format.line_spacing = paragraph.paragraph_format.line_spacing
                
                # 复制字符格式（针对每个run）
                new_paragraph.clear()  # 清空默认文本
                for run in paragraph.runs:
                    new_run = new_paragraph.add_run(run.text)

                    # 复制基本格式
                    new_run.bold = run.bold
                    new_run.italic = run.italic
                    new_run.underline = run.underline

                    # 复制字体大小
                    if run.font.size:
                        new_run.font.size = run.font.size

                    # 复制字体颜色
                    if run.font.color.rgb:
                        new_run.font.color.rgb = run.font.color.rgb

                    # 完整复制字体信息
                    copy_font_properties(run.font, new_run.font)

            # 添加分页符 (除了最后一个文档)
            if i < len(word_files) - 1:
                merged_document.add_page_break()

            print(f"已合并文档：{file_path}")

        except Exception as e:
            print(f"无法合并文档 '{file_path}': {e}")

    # 保存合并后的文档
    try:
        merged_document.save(output_filename)
        print(f"已成功合并所有文档到 '{output_filename}'")
    except Exception as e:
        print(f"无法保存合并后的文档 '{output_filename}': {e}")

if __name__ == '__main__':
    directory_path = "./documents"
    merge_word_documents(directory_path)
