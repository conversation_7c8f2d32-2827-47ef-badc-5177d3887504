import os
import glob
from docx import Document
from docx.enum.text import WD_BREAK
import copy
from docx.oxml.shared import qn
from docx.oxml import OxmlElement

def merge_word_documents(directory, output_filename="merged_document.docx"):
    """
    将指定目录下的所有 Word 文档合并为一个文档，每个文档内容用分页符分割。
    """
    merged_document = Document()

    word_files = glob.glob(os.path.join(directory, "*.docx")) + glob.glob(os.path.join(directory, "*.doc"))

    if not word_files:
        print(f"在目录 '{directory}' 中没有找到任何 Word 文档。")
        return

    for i, file_path in enumerate(word_files):
        try:
            try:
                doc = Document(file_path)
            except Exception as e:
                print(f"打开文档失败：{file_path}，尝试不指定编码打开：{e}")
                doc = Document(file_path)

            # 检查文档是否为空（更全面的检查）
            if not doc.element.body.getchildren():
                print(f"文档 '{file_path}' 为空，已跳过。")
                continue
            # 遍历文档中的每个段落
            for paragraph in doc.paragraphs:
                # print(paragraph.text)  # 打印段落的文本内容
                merged_document.add_paragraph(paragraph.text, paragraph.style)

            # for paragraph in merged_document.paragraphs:
            #     print(paragraph.text)
            # 复制文档内容，包括样式
            # for element in doc.element.body.iterchildren():
            #     new_element = copy.deepcopy(element)
            #     merged_document.element.body.append(new_element)

            # 添加分页符 (除了最后一个文档)
            if i < len(word_files) - 1:
                merged_document.add_page_break()

            print(f"已合并文档：{file_path}")

        except Exception as e:
            print(f"无法合并文档 '{file_path}': {e}")

    # 保存合并后的文档
    try:
        merged_document.save(output_filename)
        print(f"已成功合并所有文档到 '{output_filename}'")
    except Exception as e:
        print(f"无法保存合并后的文档 '{output_filename}': {e}")

if __name__ == '__main__':
    directory_path = "./documents"
    merge_word_documents(directory_path)