#!/usr/bin/env python3
"""
详细分析文档字体结构的脚本
"""

from docx import Document
from docx.oxml.shared import qn
import xml.etree.ElementTree as ET

def analyze_font_element(font_element):
    """
    详细分析字体元素的XML结构
    """
    print("字体元素XML结构:")
    if font_element is not None:
        # 打印所有属性
        for attr, value in font_element.attrib.items():
            print(f"  {attr}: {value}")
        
        # 打印子元素
        for child in font_element:
            print(f"  子元素: {child.tag}")
            for attr, value in child.attrib.items():
                print(f"    {attr}: {value}")
    else:
        print("  字体元素为空")

def detailed_font_analysis(filename):
    """
    详细分析文档字体
    """
    print(f"详细分析文档: {filename}")
    print("=" * 60)
    
    try:
        doc = Document(filename)
        
        for i, paragraph in enumerate(doc.paragraphs[:3]):  # 只分析前3个段落
            print(f"\n段落 {i+1}: '{paragraph.text[:50]}{'...' if len(paragraph.text) > 50 else ''}'")
            print("-" * 40)
            
            for j, run in enumerate(paragraph.runs):
                if run.text.strip():
                    print(f"\n  Run {j+1}: '{run.text[:30]}{'...' if len(run.text) > 30 else ''}'")
                    
                    # 基本字体信息
                    print(f"    字体名称: {run.font.name}")
                    print(f"    字体大小: {run.font.size}")
                    print(f"    粗体: {run.font.bold}")
                    print(f"    斜体: {run.font.italic}")
                    
                    # 详细分析字体元素
                    font_element = run.font.element
                    analyze_font_element(font_element)
                    
                    # 检查是否有rFonts子元素
                    rfonts = font_element.find(qn('w:rFonts'))
                    if rfonts is not None:
                        print("    rFonts 元素:")
                        for attr, value in rfonts.attrib.items():
                            print(f"      {attr}: {value}")
                    else:
                        print("    没有找到 rFonts 元素")
                    
                    print()
            
            if i >= 2:  # 只分析前3个段落
                break
                
    except Exception as e:
        print(f"分析文档时出错: {e}")
        import traceback
        traceback.print_exc()

def compare_original_and_merged():
    """
    比较原始文档和合并文档的字体结构
    """
    print("原始文档 vs 合并文档字体结构对比")
    print("=" * 80)
    
    # 分析原始文档
    detailed_font_analysis("./bk/安同良.docx")
    
    print("\n" + "=" * 80)
    
    # 分析合并文档的开头部分
    detailed_font_analysis("merged_document.docx")

if __name__ == "__main__":
    compare_original_and_merged()
