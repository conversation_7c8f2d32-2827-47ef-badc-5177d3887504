#!/usr/bin/env python3
"""
验证字体修复效果的最终脚本
"""

from docx import Document
import glob
import random

def verify_font_preservation():
    """
    验证字体保留效果
    """
    print("字体修复验证报告")
    print("=" * 60)
    
    # 1. 随机选择几个原始文档进行对比
    original_files = glob.glob('./bk/*.docx')
    sample_files = random.sample(original_files, min(5, len(original_files)))
    
    print("1. 原始文档字体分析:")
    print("-" * 30)
    
    original_fonts = {}
    for file in sample_files:
        try:
            doc = Document(file)
            file_fonts = set()
            for p in doc.paragraphs:
                for r in p.runs:
                    if r.text.strip() and r.font.name:
                        file_fonts.add(r.font.name)
            
            print(f"  {file.split('/')[-1]}: {sorted(file_fonts)}")
            for font in file_fonts:
                original_fonts[font] = original_fonts.get(font, 0) + 1
                
        except Exception as e:
            print(f"  {file}: 读取失败 - {e}")
    
    print(f"\n原始文档中发现的字体: {sorted(original_fonts.keys())}")
    
    # 2. 分析合并文档
    print("\n2. 合并文档字体分析:")
    print("-" * 30)
    
    try:
        merged_doc = Document('merged_document.docx')
        merged_fonts = {}
        total_runs = 0
        
        for p in merged_doc.paragraphs:
            for r in p.runs:
                if r.text.strip():
                    total_runs += 1
                    font_name = r.font.name or '未设置'
                    merged_fonts[font_name] = merged_fonts.get(font_name, 0) + 1
        
        print(f"  总文本段数: {total_runs}")
        print("  字体使用统计:")
        for font, count in sorted(merged_fonts.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_runs) * 100
            print(f"    {font}: {count} 次 ({percentage:.1f}%)")
        
        # 3. 验证字体保留情况
        print("\n3. 字体保留验证:")
        print("-" * 30)
        
        preserved_fonts = set(original_fonts.keys()) & set(merged_fonts.keys())
        lost_fonts = set(original_fonts.keys()) - set(merged_fonts.keys())
        new_fonts = set(merged_fonts.keys()) - set(original_fonts.keys())
        
        print(f"  ✅ 成功保留的字体: {sorted(preserved_fonts)}")
        if lost_fonts:
            print(f"  ❌ 丢失的字体: {sorted(lost_fonts)}")
        if new_fonts:
            print(f"  ➕ 新增的字体: {sorted(new_fonts)}")
        
        # 4. 字体一致性检查
        print("\n4. 字体一致性检查:")
        print("-" * 30)
        
        # 检查前几个段落的字体是否与原始文档一致
        sample_paragraphs = merged_doc.paragraphs[1:6]  # 跳过空段落
        for i, p in enumerate(sample_paragraphs):
            if p.text.strip():
                fonts_in_paragraph = set()
                for r in p.runs:
                    if r.text.strip() and r.font.name:
                        fonts_in_paragraph.add(r.font.name)
                print(f"  段落 {i+1}: '{p.text[:30]}...' 使用字体: {sorted(fonts_in_paragraph)}")
        
        # 5. 总结
        print("\n5. 修复效果总结:")
        print("-" * 30)
        
        if len(preserved_fonts) == len(original_fonts) and not lost_fonts:
            print("  ✅ 字体修复成功！所有原始字体都得到了完整保留。")
        elif len(preserved_fonts) > 0:
            print(f"  ⚠️  字体部分保留。保留了 {len(preserved_fonts)}/{len(original_fonts)} 种字体。")
        else:
            print("  ❌ 字体修复失败。没有保留原始字体。")
        
        print(f"  📊 合并了 {len(original_files)} 个文档，生成 {total_runs} 个文本段。")
        
    except Exception as e:
        print(f"分析合并文档时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_font_preservation()
